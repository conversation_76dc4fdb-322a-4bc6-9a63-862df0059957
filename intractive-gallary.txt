"use client";

import React, { useState, useRef, useEffect, createContext, useContext, memo } from "react";
import { Search, X, User, ChevronLeft, ChevronRight, Play, Pause } from "lucide-react";
import { motion, AnimatePresence, useMotionValue, wrap, animate, cubicBezier } from "framer-motion";
import { cn } from "@/lib/utils";
import { cva, type VariantProps } from "class-variance-authority";

// Types
interface MediaData {
  id: string;
  src: string;
  alt: string;
  type: 'image' | 'video';
  user: {
    name: string;
    avatar: string;
  };
  prompt?: string;
  createdAt?: string;
  thumbnail?: string; // For video thumbnails
}

interface GalleryProps {
  media?: MediaData[];
  className?: string;
}

// Sample data
const sampleMedia: MediaData[] = [
  {
    id: "1",
    src: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop",
    alt: "Mountain landscape",
    type: "image",
    user: { name: "alex_photo", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" },
    prompt: "A serene mountain landscape at sunset"
  },
  {
    id: "2", 
    src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
    alt: "Big Buck Bunny",
    type: "video",
    thumbnail: "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=800&fit=crop",
    user: { name: "wave_rider", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" },
    prompt: "Animated short film about a bunny"
  },
  {
    id: "3",
    src: "https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400&h=500&fit=crop", 
    alt: "Forest path",
    type: "image",
    user: { name: "nature_lover", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" },
    prompt: "Mystical forest path with dappled sunlight"
  },
  {
    id: "4",
    src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.mp4",
    alt: "Elephants Dream",
    type: "video",
    thumbnail: "https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=700&fit=crop",
    user: { name: "desert_wanderer", avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=32&h=32&fit=crop&crop=face" },
    prompt: "Surreal animated short film"
  },
  {
    id: "5",
    src: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=550&fit=crop",
    alt: "City skyline",
    type: "image",
    user: { name: "urban_explorer", avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=32&h=32&fit=crop&crop=face" },
    prompt: "Futuristic city skyline at night"
  },
  {
    id: "6",
    src: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
    alt: "For Bigger Blazes",
    type: "video",
    thumbnail: "https://images.unsplash.com/photo-1518837695005-2083093ee35b?w=400&h=650&fit=crop",
    user: { name: "digital_artist", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=32&h=32&fit=crop&crop=face" },
    prompt: "Dynamic fire and motion graphics"
  }
];

// Header Component
const Header = () => {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-md border-b border-border/20">
      <div className="max-w-7xl mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center">
          <X className="w-6 h-6" />
        </div>
        
        <div className="flex-1 max-w-md mx-8">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search"
              className="w-full pl-10 pr-4 py-2 bg-muted/50 border border-border/30 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20"
            />
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <a href="#" className="text-sm text-muted-foreground hover:text-foreground">API</a>
          <a href="#" className="text-sm text-muted-foreground hover:text-foreground">Docs</a>
          <button className="px-4 py-2 bg-blue-600 text-white text-sm rounded-full hover:bg-blue-700 transition-colors">
            Join Beta
          </button>
          <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
            <User className="w-4 h-4" />
          </div>
        </div>
      </div>
    </header>
  );
};

// Modal Component
const MediaModal = ({ media, isOpen, onClose }: { media: MediaData | null; isOpen: boolean; onClose: () => void }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (isOpen && media?.type === 'video' && videoRef.current) {
      videoRef.current.play().catch(error => console.error("Video autoplay failed:", error));
    } else if (!isOpen && media?.type === 'video' && videoRef.current) {
      videoRef.current.pause();
      videoRef.current.currentTime = 0; // Reset video on close
    }
  }, [isOpen, media]);

  if (!media) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-hidden flex" // Changed to flex
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex-1 flex items-center justify-center min-w-0"> {/* Added min-w-0 */}
                {media.type === 'video' ? (
                  <div className="relative w-full h-full flex items-center justify-center">
                    <video
                      ref={videoRef}
                      src={media.src}
                      className="max-w-full max-h-[70vh] object-contain" // Changed to object-contain for aspect ratio
                      controls
                      loop // Added loop for video in modal
                    />
                  </div>
                ) : (
                  <img
                    src={media.src}
                    alt={media.alt}
                    className="max-w-full max-h-[70vh] object-contain" // Changed to object-contain for aspect ratio
                  />
                )}
              </div>
              <div className="w-80 p-6 border-l border-border flex-shrink-0"> {/* Added flex-shrink-0 */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <img
                      src={media.user.avatar}
                      alt={media.user.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <span className="font-medium">{media.user.name}</span>
                  </div>
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-muted rounded-full"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
                <div className="mb-4">
                  <span className="inline-block px-2 py-1 bg-muted text-xs rounded-full mb-2">
                    {media.type}
                  </span>
                </div>
                {media.prompt && (
                  <div className="mb-4">
                    <h3 className="font-medium mb-2">Prompt</h3>
                    <p className="text-sm text-muted-foreground">{media.prompt}</p>
                  </div>
                )}
                <div className="flex gap-2">
                  <button className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    Recreate
                  </button>
                  <button className="px-4 py-2 border border-border rounded-lg hover:bg-muted transition-colors">
                    Save
                  </button>
                </div>
              </div>
            </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Video Player Component
const VideoPlayer = ({ media, onPlay }: { media: MediaData; onPlay: () => void }) => {
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (videoElement) {
      videoElement.loop = true;
      videoElement.muted = true;
      // Auto-play the video when component mounts
      videoElement.play().catch(error => {
        console.error("Video autoplay failed:", error);
      });
    }
  }, []);

  return (
    <div className="relative">
      <video
        ref={videoRef}
        src={media.src}
        className="w-full h-auto object-cover"
        autoPlay
        loop
        muted
        playsInline
        onClick={() => onPlay()}
      />
    </div>
  );
};

// Uniform Grid Component
const UniformGrid = ({ media, onMediaClick }: { media: MediaData[]; onMediaClick: (media: MediaData) => void }) => {
  const [playingVideo, setPlayingVideo] = useState<string | null>(null);

  const handleVideoPlay = (mediaId: string) => {
    setPlayingVideo(mediaId);
  };

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
      {media.map((item, index) => (
        <motion.div
          key={item.id}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
          className="group cursor-pointer"
          onClick={() => onMediaClick(item)}
        >
          <div className="relative overflow-hidden rounded-lg bg-muted aspect-[3/4] h-80">
            {item.type === 'video' ? (
              <video
                src={item.src}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                autoPlay
                loop
                muted
                playsInline
              />
            ) : (
              <img
                src={item.src}
                alt={item.alt}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
                loading="lazy"
              />
            )}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300" />
            <div className="absolute bottom-3 left-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <img
                src={item.user.avatar}
                alt={item.user.name}
                className="w-6 h-6 rounded-full border-2 border-white"
              />
              <span className="text-white text-sm font-medium drop-shadow-lg">
                {item.user.name}
              </span>
            </div>
            <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex gap-2">
                <span className="px-2 py-1 bg-white/20 backdrop-blur-sm rounded-full text-white text-xs">
                  {item.type}
                </span>
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  );
};

// Main Gallery Component
const AIGenerationGallery = ({ media = sampleMedia, className }: GalleryProps) => {
  const [selectedMedia, setSelectedMedia] = useState<MediaData | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleMediaClick = (mediaItem: MediaData) => {
    setSelectedMedia(mediaItem);
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedMedia(null);
  };

  // Duplicate media to create infinite scroll effect
  const duplicatedMedia = [...media, ...media, ...media];

  return (
    <div className={cn("min-h-screen bg-white", className)}>
      <Header />
      
      <main className="pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-12">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4"
            >
              Find, recreate, or share generations with millions.
            </motion.h1>
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full hover:from-blue-700 hover:to-purple-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <ChevronRight className="w-4 h-4" />
              Start with an image
            </motion.button>
          </div>

                    <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
                        <UniformGrid media={duplicatedMedia} onMediaClick={handleMediaClick} />
          </motion.div>
        </div>
      </main>

            <MediaModal
        media={selectedMedia}
        isOpen={isModalOpen}
        onClose={closeModal}
      />
    </div>
  );
};

// Demo Component
export default function AIGenerationGalleryDemo() {
  return <AIGenerationGallery />;
}
